#!/usr/bin/env bash
set -Eeuo pipefail

# Build & run a 3-level CMake project.
# Usage:
#   ./build_and_run.sh [PROJECT_ROOT] [BUILD_DIR] [CONFIG]
# Defaults:
#   PROJECT_ROOT = <script_dir>/three-level-demo
#   BUILD_DIR    = <PROJECT_ROOT>/build
#   CONFIG       = Debug   (used for multi-config generators, e.g. Visual Studio)

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${1:-$SCRIPT_DIR/three-level-demo}"
BUILD_DIR="${2:-$PROJECT_ROOT/build}"
CONFIG="${3:-Debug}"

# Detect Windows (MSYS/MINGW/Cygwin or native)
UNAME_S="$(uname -s 2>/dev/null || echo "")"
if [[ "$OS" == "Windows_NT" || "$UNAME_S" =~ MINGW|MSYS|CYGWIN ]]; then
  EXE_EXT=".exe"
else
  EXE_EXT=""
fi

# 1) Ensure project exists; if not, create it
if [[ ! -f "$PROJECT_ROOT/CMakeLists.txt" ]]; then
  echo "ℹ️  Project not found at $PROJECT_ROOT. Creating it now..."
  "$SCRIPT_DIR/create_three_level_project.sh" "$PROJECT_ROOT"
fi

# 2) Configure
echo "🔧 Configuring with CMake..."
cmake -S "$PROJECT_ROOT" -B "$BUILD_DIR"

# 3) Build (always pass --config for multi-config generators)
echo "🛠️  Building (config: $CONFIG)…"
cmake --build "$BUILD_DIR" --target myapp --config "$CONFIG" -j

# 4) Resolve the executable path
resolve_app_path() {
  local base="$BUILD_DIR"
  local cand=""

  # Plain single-config (Unix Makefiles, Ninja)
  cand="$base/app/myapp$EXE_EXT"
  [[ -x "$cand" ]] && { echo "$cand"; return; }

  # Multi-config (e.g., VS, Ninja Multi-Config)
  cand="$base/$CONFIG/app/myapp$EXE_EXT"
  [[ -x "$cand" ]] && { echo "$cand"; return; }

  # Some generators put target dirs differently (rare)
  for cfg in "$CONFIG" Debug Release RelWithDebInfo MinSizeRel; do
    cand="$base/$cfg/app/myapp$EXE_EXT"
    [[ -x "$cand" ]] && { echo "$cand"; return; }
  done

  # Last resort: search
  cand="$(command -v find  >/dev/null 2>&1 && find "$BUILD_DIR" -type f -name "myapp$EXE_EXT" | head -n1 || true)"
  [[ -n "$cand" && -x "$cand" ]] && { echo "$cand"; return; }

  echo ""
}

APP_PATH="$(resolve_app_path)"
if [[ -z "$APP_PATH" ]]; then
  echo "❌ Unable to locate built executable 'myapp$EXE_EXT'."
  echo "   Looked under: $BUILD_DIR/app and common config folders (config=$CONFIG)."
  exit 1
fi

echo "▶️  Running: $APP_PATH"
"$APP_PATH"

# 5) Optionally run the checker if present
CHECKER="$SCRIPT_DIR/cmake-link-check.sh"
if [[ -x "$CHECKER" ]]; then
  echo "🔎 Running checker: $CHECKER"
  if "$CHECKER" "$PROJECT_ROOT"; then
    echo "🎉 Checker reported no problems (as expected)."
  else
    code=$?
    echo "❌ Checker reported issues (exit code $code), but this project should be clean."
    exit $code
  fi
else
  echo "ℹ️  Checker 'cmake-link-check.sh' not found or not executable in $SCRIPT_DIR — skipping."
fi

echo "✅ Done."
