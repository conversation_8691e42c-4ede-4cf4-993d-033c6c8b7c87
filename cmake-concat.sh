#!/usr/bin/env bash
set -euo pipefail

# Ако скриптът не се стартира с bash (напр. sh/zsh), преизпълни го с bash.
if [ -z "${BASH_VERSION:-}" ]; then
  exec bash "$0" "$@"
fi

# Използване:
#   ./collect_cmake_to_file.sh                      # търси в текущата директория, пише в ./all_CMakeLists.txt
#   ./collect_cmake_to_file.sh /path/to/dir         # подаваш директория, пише в ./all_CMakeLists.txt
#   ./collect_cmake_to_file.sh /path/to/dir out.txt # подаваш директория + изходен файл

TARGET_DIR="${1:-.}"
OUTPUT_FILE="${2:-all_CMakeLists.txt}"

if [[ ! -d "$TARGET_DIR" ]]; then
  echo "Грешка: директорията '$TARGET_DIR' не съществува." >&2
  exit 2
fi

# Нулираме/създаваме изходния файл (в текущата директория)
: > "$OUTPUT_FILE"

echo "Търся CMakeLists.txt файлове в: $TARGET_DIR"
echo "Ще запиша в: $OUTPUT_FILE"
echo "------------------------------------------------------"

# Брояч
count=0

# Функция, която обединява съдържанието (в отделна мини-обвивка sh)
merge_fn='
  out="$1"; shift
  for f in "$@"; do
    # Показваме кой файл добавяме
    echo "Добавям: $f" >&2
    {
      echo "#### START FILE: $f ####"
      cat "$f"
      echo
      echo "#### END FILE: $f ####"
      echo
    } >> "$out"
  done
'

# Изпълняваме на партиди ({} +), за да е бързо и безопасно към интервали в имената
# Поддържа и BSD/macOS find.
if ! find "$TARGET_DIR" -type f -name 'CMakeLists.txt' -exec sh -c "$merge_fn" _ "$OUTPUT_FILE" {} + 2> >(tee /dev/stderr >/dev/null); then
  echo "Грешка при обхождане на файловете." >&2
  exit 3
fi

# Изчисляваме броя намерени файлове (без да разчитаме на GNU-специфики)
count="$(grep -c '^#### START FILE:' -- "$OUTPUT_FILE" || true)"

if [[ "$count" == "0" ]]; then
  echo "❌ Не са намерени файлове CMakeLists.txt в '$TARGET_DIR'." >&2
  # Празен файл не е полезен — изтриваме го
  rm -f -- "$OUTPUT_FILE"
  exit 1
fi

echo "------------------------------------------------------"
echo "✅ Обединени файлове: $count"
echo "📄 Резултатният файл е: $( (command -v realpath >/dev/null && realpath -- "$OUTPUT_FILE") || echo "$OUTPUT_FILE" )"
echo "------------------------------------------------------"
echo "Последни 10 реда от файла:"
tail -n 10 -- "$OUTPUT_FILE"
