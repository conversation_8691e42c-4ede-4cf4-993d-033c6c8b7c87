﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{5A8D5EB2-B45F-385A-8534-46AEB4<PERSON>C1CA}"
	ProjectSection(ProjectDependencies) = postProject
		{79CBE74F-2412-3930-BDF9-628800F16C51} = {79CBE74F-2412-3930-BDF9-628800F16C51}
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49} = {E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}
		{7F5F5B42-F719-3467-BF15-E3F790A276BE} = {7F5F5B42-F719-3467-BF15-E3F790A276BE}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{79CBE74F-2412-3930-BDF9-628800F16C51}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "mathlib", "lib\math\mathlib.vcxproj", "{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}"
	ProjectSection(ProjectDependencies) = postProject
		{79CBE74F-2412-3930-BDF9-628800F16C51} = {79CBE74F-2412-3930-BDF9-628800F16C51}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "myapp", "app\myapp.vcxproj", "{7F5F5B42-F719-3467-BF15-E3F790A276BE}"
	ProjectSection(ProjectDependencies) = postProject
		{79CBE74F-2412-3930-BDF9-628800F16C51} = {79CBE74F-2412-3930-BDF9-628800F16C51}
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49} = {E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5A8D5EB2-B45F-385A-8534-46AEB4FAC1CA}.Debug|x64.ActiveCfg = Debug|x64
		{5A8D5EB2-B45F-385A-8534-46AEB4FAC1CA}.Release|x64.ActiveCfg = Release|x64
		{5A8D5EB2-B45F-385A-8534-46AEB4FAC1CA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5A8D5EB2-B45F-385A-8534-46AEB4FAC1CA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{79CBE74F-2412-3930-BDF9-628800F16C51}.Debug|x64.ActiveCfg = Debug|x64
		{79CBE74F-2412-3930-BDF9-628800F16C51}.Debug|x64.Build.0 = Debug|x64
		{79CBE74F-2412-3930-BDF9-628800F16C51}.Release|x64.ActiveCfg = Release|x64
		{79CBE74F-2412-3930-BDF9-628800F16C51}.Release|x64.Build.0 = Release|x64
		{79CBE74F-2412-3930-BDF9-628800F16C51}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{79CBE74F-2412-3930-BDF9-628800F16C51}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{79CBE74F-2412-3930-BDF9-628800F16C51}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{79CBE74F-2412-3930-BDF9-628800F16C51}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}.Debug|x64.ActiveCfg = Debug|x64
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}.Debug|x64.Build.0 = Debug|x64
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}.Release|x64.ActiveCfg = Release|x64
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}.Release|x64.Build.0 = Release|x64
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E8A99D49-AA60-3A53-B5CD-FD1EAB6D2D49}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7F5F5B42-F719-3467-BF15-E3F790A276BE}.Debug|x64.ActiveCfg = Debug|x64
		{7F5F5B42-F719-3467-BF15-E3F790A276BE}.Debug|x64.Build.0 = Debug|x64
		{7F5F5B42-F719-3467-BF15-E3F790A276BE}.Release|x64.ActiveCfg = Release|x64
		{7F5F5B42-F719-3467-BF15-E3F790A276BE}.Release|x64.Build.0 = Release|x64
		{7F5F5B42-F719-3467-BF15-E3F790A276BE}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7F5F5B42-F719-3467-BF15-E3F790A276BE}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7F5F5B42-F719-3467-BF15-E3F790A276BE}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7F5F5B42-F719-3467-BF15-E3F790A276BE}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {66FC41EB-298C-36C0-9249-5F38738F7363}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
