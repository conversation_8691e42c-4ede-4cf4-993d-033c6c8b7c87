PROJ="$(pwd)/three-level-demo"
rm -rf "$PROJ"
mkdir -p "$PROJ"/{lib/math,app,libs}

# root CMake
cat > "$PROJ/CMakeLists.txt" <<'EOF'
cmake_minimum_required(VERSION 3.16)
project(three_level_demo LANGUAGES CXX)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
add_subdirectory(lib)
add_subdirectory(app)
EOF

# lib aggregator
cat > "$PROJ/lib/CMakeLists.txt" <<'EOF'
add_subdirectory(math)
EOF

# math lib
cat > "$PROJ/lib/math/CMakeLists.txt" <<'EOF'
add_library(mathlib STATIC math.cpp)
target_include_directories(mathlib PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
EOF

cat > "$PROJ/lib/math/math.hpp" <<'EOF'
#pragma once
int add(int a, int b);
EOF

cat > "$PROJ/lib/math/math.cpp" <<'EOF'
#include "math.hpp"
int add(int a, int b) { return a + b; }
EOF

# app (валиден вариант)
cat > "$PROJ/app/CMakeLists.txt" <<'EOF'
add_executable(myapp main.cpp)
target_link_libraries(myapp PRIVATE mathlib)
EOF

cat > "$PROJ/app/main.cpp" <<'EOF'
#include <iostream>
#include "math.hpp"
int main(){ std::cout << "3 + 4 = " << add(3,4) << "\n"; return 0; }
EOF
