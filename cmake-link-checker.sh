#!/usr/bin/env bash
# CMake linker checker – Windows-safe (handles drive-letter paths in trace)
set -Eeuo pipefail

PROJECT_ROOT="${1:-$(pwd)}"
BUILD_DIR="$(mktemp -d -t cmake-check-XXXXXX)"
TRACE_FILE="$BUILD_DIR/trace.log"

cleanup() { rm -rf "$BUILD_DIR" || true; }
trap cleanup EXIT

command -v cmake >/dev/null || { echo "❌ 'cmake' not found in PATH"; exit 1; }

# Init arrays early (safe with set -u)
unset DEFINED_TARGETS LINK_REFS 2>/dev/null || true
declare -gA DEFINED_TARGETS=()   # target -> "file:line"
declare -ga LINK_REFS=()         # "file|line|consumer|item|raw"

# Version info
cmv="$(cmake --version | head -n1 | sed -E 's/.* ([0-9]+\.[0-9]+(\.[0-9]+)?).*/\1/')"
echo "ℹ️  Using CMake version $cmv (requires >= 3.16 for --trace-expand)"

# Sanity
[[ -f "$PROJECT_ROOT/CMakeLists.txt" ]] || { echo "❌ No CMakeLists.txt at: $PROJECT_ROOT"; exit 1; }

echo "🔧 Running CMake trace..."
(
  set +e
  cmake -S "$PROJECT_ROOT" -B "$BUILD_DIR" --trace --trace-expand \
    1>/dev/null 2>"$TRACE_FILE"
)

[[ -s "$TRACE_FILE" ]] || { echo "❌ Empty trace output. Check project path."; exit 1; }

is_file_like() {
  local t="${1-}"
  # absolute on POSIX or Windows drive letter; or file-like ext
  if [[ "$t" == /* || "$t" =~ ^[A-Za-z]:[\\/].* || "$t" == .* || "$t" == *.* ]]; then
    case "$t" in
      *.a|*.so|*.dylib|*.lib|*.dll|*.framework|*.bc|*.lo|*.o) return 0 ;;
    esac
  fi
  return 1
}

is_skippable_token() {
  local t="${1-}"
  case "$t" in
    PUBLIC|PRIVATE|INTERFACE|LINK_PRIVATE|LINK_PUBLIC|debug|optimized|general|UNKNOWN|STATIC|SHARED|MODULE|OBJECT|INTERFACE) return 0 ;;
  esac
  [[ "$t" == -l* || "$t" == -Wl,* || "$t" == *::* || "$t" == \$\<* || "$t" == \$\{* ]]
}

norm_token() {
  local t="${1-}"
  t="${t%\"}"; t="${t#\"}"
  t="${t%\'}"; t="${t#\'}"
  t="${t%%,}"; t="${t#\(}"; t="${t%\)}"
  echo "$t"
}

# Parse text trace lines (handles Windows drive-letter paths)
# Format: D:/path/CMakeLists.txt(34):  command(args...)
while IFS= read -r line; do
  [[ "$line" =~ ^(.+\([0-9]+\)):\ +([A-Za-z0-9_]+)\((.*)\)$ ]] || continue
  loc="${BASH_REMATCH[1]}"
  cmd="${BASH_REMATCH[2]}"
  args="${BASH_REMATCH[3]}"

  file="${loc%%(*}"
  line_no="${loc##*(}"; line_no="${line_no%)}"

  # normalize backslashes that might slip in
  file="${file//\\//}"

  read -r -a TOKS <<< "$args"
  [[ ${#TOKS[@]} -eq 0 ]] && continue

  case "$cmd" in
    add_library)
      for tok in "${TOKS[@]}"; do
        tok="$(norm_token "$tok")"
        case "$tok" in
          ""|STATIC|SHARED|MODULE|OBJECT|INTERFACE|ALIAS|IMPORTED|GLOBAL|EXCLUDE_FROM_ALL) ;;
          *) DEFINED_TARGETS["$tok"]="$file:$line_no"; break ;;
        esac
      done
      ;;
    add_executable)
      for tok in "${TOKS[@]}"; do
        tok="$(norm_token "$tok")"
        case "$tok" in ""|WIN32|MACOSX_BUNDLE|EXCLUDE_FROM_ALL|IMPORTED|ALIAS) ;;
        *) DEFINED_TARGETS["$tok"]="$file:$line_no"; break ;;
        esac
      done
      ;;
    target_link_libraries)
      consumer="$(norm_token "${TOKS[0]}")"
      for ((i=1;i<${#TOKS[@]};i++)); do
        tok="$(norm_token "${TOKS[$i]}")"
        [[ -z "$tok" ]] && continue
        if is_skippable_token "$tok"; then continue; fi
        LINK_REFS+=("${file}|${line_no}|${consumer}|${tok}|${args}")
      done
      ;;
  esac
done < "$TRACE_FILE"

echo "🔎 Project: $PROJECT_ROOT"
echo "📝 Trace lines: $(wc -l < "$TRACE_FILE")"
echo "✅ Defined targets: ${#DEFINED_TARGETS[@]}"
echo

missing=0
for ref in "${LINK_REFS[@]}"; do
  IFS="|" read -r file line consumer item raw <<< "$ref"

  if [[ -z "${DEFINED_TARGETS[$consumer]+_}" ]]; then
    echo "⚠️  $file:$line → target_link_libraries() for undefined consumer: '$consumer'"
  fi

  if is_file_like "$item"; then
    # Resolve relative path (POSIX or Windows)
    if [[ "$item" != /* && ! "$item" =~ ^[A-Za-z]:[\\/].* ]]; then
      base_dir="$(dirname "$file")"
      candidate="$base_dir/$item"
    else
      candidate="$item"
    fi
    candidate="${candidate//\\//}"
    if [[ ! -e "$candidate" ]]; then
      echo "❌ Missing file path: $item"
      echo "    ↳ referenced at: $file:$line"
      echo "    ↳ call: target_link_libraries($raw)"
      echo
      ((missing++))
    fi
  else
    if ! is_skippable_token "$item"; then
      if [[ -z "${DEFINED_TARGETS[$item]+_}" ]]; then
        echo "❌ Missing target: $item"
        echo "    ↳ referenced at: $file:$line"
        echo "    ↳ call: target_link_libraries($raw)"
        echo
        ((missing++))
      fi
    fi
  fi
done

if [[ ${#LINK_REFS[@]} -eq 0 ]]; then
  echo "ℹ️  No target_link_libraries() calls were traced. Are they inside conditional branches?"
fi

if [[ $missing -eq 0 ]]; then
  echo "🎉 No missing targets or file paths detected."
else
  echo "🚨 Problems found: $missing"
  exit 2
fi
